{"name": "andromeda", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "lint-staged": "lint-staged", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "type-check": "astro check", "lint": "eslint .", "format": "prettier --write .", "clean": "rm -rf dist .astro", "prepare": "husky"}, "dependencies": {"@astrojs/mdx": "^4.3.1", "@astrojs/react": "^4.3.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.3", "@types/canvas-confetti": "^1.9.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/three": "^0.178.1", "astro": "^5.12.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.3", "three": "^0.178.0"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@eslint/js": "^9.31.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.14", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}