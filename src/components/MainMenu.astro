---
// Main menu component for the space exploration game
---

<div class="main-menu">
  <h1>Space Exploration Game</h1>
  <div class="menu-buttons">
    <button id="start-game" class="menu-button">Start Game</button>
    <button id="settings" class="menu-button">Settings</button>
  </div>
</div>

<style>
  .main-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: white;
  }

  h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    text-align: center;
  }

  .menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .menu-button {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .menu-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }
</style>

<script>
  document.getElementById('start-game')?.addEventListener('click', () => {
    window.location.href = '/solar-system';
  });

  document.getElementById('settings')?.addEventListener('click', () => {
    // Settings functionality will be implemented later
    alert('Settings coming soon!');
  });
</script>