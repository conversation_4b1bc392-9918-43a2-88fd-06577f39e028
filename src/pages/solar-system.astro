---
import '../styles/global.css';
import SolarSystemScene from '../components/SolarSystemScene.tsx';
---

<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>Solar System - Space Exploration Game</title>
	</head>

	<body>
		<div id="solar-system-container">
			<SolarSystemScene client:load />
			
			<!-- UI overlay for controls and info -->
			<div class="ui-overlay">
				<button id="back-to-menu" class="ui-button">Back to Menu</button>
				<div class="controls-hint">
					<p>Click and drag to explore the solar system</p>
				</div>
			</div>
		</div>

		<style>
			body {
				margin: 0;
				padding: 0;
				overflow: hidden;
			}

			#solar-system-container {
				position: relative;
				width: 100vw;
				height: 100vh;
			}

			.ui-overlay {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				pointer-events: none;
				z-index: 100;
			}

			.ui-button {
				position: absolute;
				top: 20px;
				left: 20px;
				padding: 10px 20px;
				background: rgba(0, 0, 0, 0.7);
				color: white;
				border: 1px solid rgba(255, 255, 255, 0.3);
				border-radius: 5px;
				cursor: pointer;
				pointer-events: auto;
				transition: all 0.3s ease;
			}

			.ui-button:hover {
				background: rgba(0, 0, 0, 0.9);
				border-color: rgba(255, 255, 255, 0.5);
			}

			.controls-hint {
				position: absolute;
				bottom: 20px;
				left: 20px;
				color: white;
				background: rgba(0, 0, 0, 0.5);
				padding: 10px;
				border-radius: 5px;
				pointer-events: none;
			}

			.controls-hint p {
				margin: 0;
				font-size: 14px;
			}
		</style>

		<script>
			document.getElementById('back-to-menu')?.addEventListener('click', () => {
				window.location.href = '/';
			});
		</script>
	</body>
</html>